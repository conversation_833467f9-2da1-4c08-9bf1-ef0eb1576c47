const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import database connections
const { connectMongoDB, connectRedis } = require('./config/database');
const { initializeBuckets } = require('./config/minio');

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? process.env.FRONTEND_URL 
    : ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// Logging middleware
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Import routes
const uploadRoutes = require('./routes/upload');
const authRoutes = require('./routes/auth');
const pointsRoutes = require('./routes/points');
const musicRoutes = require('./routes/music');
const musicUploadRoutes = require('./routes/musicUpload');
const audioQualityRoutes = require('./routes/audioQuality');
const playlistRoutes = require('./routes/playlists');
const playRoutes = require('./routes/play');
const queueRoutes = require('./routes/queue');
const historyRoutes = require('./routes/history');
const statsRoutes = require('./routes/stats');
const recommendationRoutes = require('./routes/recommendations');
const followRoutes = require('./routes/follows');
const commentRoutes = require('./routes/comments');
const likeRoutes = require('./routes/likes');
const shareRoutes = require('./routes/shares');

// API routes
app.get('/api/v1', (req, res) => {
  res.json({
    message: 'MusicDou API v1.0',
    version: '1.0.0',
    status: 'running'
  });
});

// Upload routes
app.use('/api/v1/upload', uploadRoutes);

// Authentication routes
app.use('/api/v1/auth', authRoutes);

// Points routes
app.use('/api/v1/points', pointsRoutes);

// Music upload routes (放在前面，避免被通用路由拦截)
app.use('/api/v1/music', musicUploadRoutes);

// Music routes
app.use('/api/v1/music', musicRoutes);

// Audio quality routes
app.use('/api/v1/audio-quality', audioQualityRoutes);

// Playlist routes
app.use('/api/v1/playlists', playlistRoutes);

// Play routes
app.use('/api/v1/play', playRoutes);

// Queue routes
app.use('/api/v1/queue', queueRoutes);

// History routes
app.use('/api/v1/history', historyRoutes);

// Stats routes
app.use('/api/v1/stats', statsRoutes);

// Recommendation routes
app.use('/api/v1/recommendations', recommendationRoutes);

// Follow routes
app.use('/api/v1/follows', followRoutes);

// Comment routes
app.use('/api/v1/comments', commentRoutes);

// Like routes
app.use('/api/v1/likes', likeRoutes);

// Share routes
app.use('/api/v1/shares', shareRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // Default error response
  const errorResponse = {
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' 
      ? 'Something went wrong!' 
      : err.message,
    timestamp: new Date().toISOString()
  };

  // Add stack trace in development
  if (process.env.NODE_ENV !== 'production') {
    errorResponse.stack = err.stack;
  }

  res.status(err.status || 500).json(errorResponse);
});

// Initialize database connections and start server
const startServer = async () => {
  try {
    // Connect to MongoDB
    await connectMongoDB();

    // Connect to Redis
    const redisClient = await connectRedis();

    // Make Redis client available to the app
    app.locals.redisClient = redisClient;

    // Initialize MinIO buckets
    await initializeBuckets();

    // Start server
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
      console.log(`🚀 MusicDou server is running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/health`);
      console.log(`🎵 API endpoint: http://localhost:${PORT}/api/v1`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the application
startServer();

module.exports = app;
