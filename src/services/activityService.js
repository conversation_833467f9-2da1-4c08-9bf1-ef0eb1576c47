const Activity = require('../models/Activity');
const User = require('../models/User');
const Music = require('../models/Music');
const Playlist = require('../models/Playlist');
const Comment = require('../models/Comment');

/**
 * 用户动态服务
 * 负责自动生成和管理用户动态
 */
class ActivityService {
  
  /**
   * 创建上传音乐动态
   */
  static async createUploadMusicActivity(userId, musicId, metadata = {}) {
    try {
      const music = await Music.findById(musicId).lean();
      if (!music) {
        throw new Error('Music not found');
      }

      const activityData = {
        user: userId,
        type: 'upload_music',
        title: `上传了新音乐《${music.title}》`,
        description: `艺术家：${music.artist}${music.album ? ` | 专辑：${music.album}` : ''}`,
        target: {
          type: 'music',
          id: musicId,
          snapshot: {
            title: music.title,
            artist: music.artist,
            album: music.album,
            coverImage: music.coverImage,
            duration: music.duration,
            quality: music.quality
          }
        },
        privacy: 'public',
        source: metadata.source || 'web',
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating upload music activity:', error);
      throw error;
    }
  }

  /**
   * 创建创建歌单动态
   */
  static async createPlaylistActivity(userId, playlistId, metadata = {}) {
    try {
      const playlist = await Playlist.findById(playlistId).lean();
      if (!playlist) {
        throw new Error('Playlist not found');
      }

      const activityData = {
        user: userId,
        type: 'create_playlist',
        title: `创建了新歌单《${playlist.name}》`,
        description: playlist.description || '',
        target: {
          type: 'playlist',
          id: playlistId,
          snapshot: {
            name: playlist.name,
            description: playlist.description,
            coverImage: playlist.coverImage,
            songCount: playlist.songs ? playlist.songs.length : 0,
            isPublic: playlist.isPublic
          }
        },
        privacy: playlist.isPublic ? 'public' : 'followers',
        source: metadata.source || 'web',
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating playlist activity:', error);
      throw error;
    }
  }

  /**
   * 创建关注用户动态
   */
  static async createFollowUserActivity(followerId, followingId, metadata = {}) {
    try {
      const followingUser = await User.findById(followingId).lean();
      if (!followingUser) {
        throw new Error('Following user not found');
      }

      const activityData = {
        user: followerId,
        type: 'follow_user',
        title: `关注了 ${followingUser.profile?.displayName || followingUser.username}`,
        description: followingUser.profile?.bio || '',
        target: {
          type: 'user',
          id: followingId,
          snapshot: {
            username: followingUser.username,
            displayName: followingUser.profile?.displayName,
            avatar: followingUser.avatar,
            bio: followingUser.profile?.bio
          }
        },
        privacy: 'public',
        source: metadata.source || 'web',
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating follow user activity:', error);
      throw error;
    }
  }

  /**
   * 创建点赞音乐动态
   */
  static async createLikeMusicActivity(userId, musicId, metadata = {}) {
    try {
      const music = await Music.findById(musicId).lean();
      if (!music) {
        throw new Error('Music not found');
      }

      const activityData = {
        user: userId,
        type: 'like_music',
        title: `点赞了音乐《${music.title}》`,
        description: `艺术家：${music.artist}`,
        target: {
          type: 'music',
          id: musicId,
          snapshot: {
            title: music.title,
            artist: music.artist,
            album: music.album,
            coverImage: music.coverImage
          }
        },
        privacy: 'followers', // 点赞动态默认仅关注者可见
        source: metadata.source || 'web',
        weight: 0.8, // 点赞动态权重较低
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating like music activity:', error);
      throw error;
    }
  }

  /**
   * 创建评论音乐动态
   */
  static async createCommentMusicActivity(userId, commentId, metadata = {}) {
    try {
      const comment = await Comment.findById(commentId)
        .populate('musicId', 'title artist coverImage')
        .lean();
      
      if (!comment) {
        throw new Error('Comment not found');
      }

      const activityData = {
        user: userId,
        type: 'comment_music',
        title: `评论了音乐《${comment.musicId.title}》`,
        description: comment.content.length > 100 ? 
          comment.content.substring(0, 100) + '...' : 
          comment.content,
        target: {
          type: 'comment',
          id: commentId,
          snapshot: {
            content: comment.content,
            musicTitle: comment.musicId.title,
            musicArtist: comment.musicId.artist,
            musicCoverImage: comment.musicId.coverImage,
            level: comment.level
          }
        },
        privacy: 'public',
        source: metadata.source || 'web',
        weight: 1.2, // 评论动态权重较高
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating comment music activity:', error);
      throw error;
    }
  }

  /**
   * 创建分享音乐动态
   */
  static async createShareMusicActivity(userId, musicId, metadata = {}) {
    try {
      const music = await Music.findById(musicId).lean();
      if (!music) {
        throw new Error('Music not found');
      }

      const activityData = {
        user: userId,
        type: 'share_music',
        title: `分享了音乐《${music.title}》`,
        description: `艺术家：${music.artist}${metadata.shareText ? ` | ${metadata.shareText}` : ''}`,
        target: {
          type: 'music',
          id: musicId,
          snapshot: {
            title: music.title,
            artist: music.artist,
            album: music.album,
            coverImage: music.coverImage,
            duration: music.duration
          }
        },
        privacy: 'public',
        source: metadata.source || 'web',
        weight: 1.5, // 分享动态权重高
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false,
          extra: {
            shareText: metadata.shareText,
            sharePlatform: metadata.sharePlatform
          }
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating share music activity:', error);
      throw error;
    }
  }

  /**
   * 创建收藏歌单动态
   */
  static async createFavoritePlaylistActivity(userId, playlistId, metadata = {}) {
    try {
      const playlist = await Playlist.findById(playlistId)
        .populate('createdBy', 'username profile.displayName')
        .lean();
      
      if (!playlist) {
        throw new Error('Playlist not found');
      }

      const activityData = {
        user: userId,
        type: 'favorite_playlist',
        title: `收藏了歌单《${playlist.name}》`,
        description: `创建者：${playlist.createdBy.profile?.displayName || playlist.createdBy.username}`,
        target: {
          type: 'playlist',
          id: playlistId,
          snapshot: {
            name: playlist.name,
            description: playlist.description,
            coverImage: playlist.coverImage,
            creatorName: playlist.createdBy.profile?.displayName || playlist.createdBy.username,
            songCount: playlist.songs ? playlist.songs.length : 0
          }
        },
        privacy: 'followers',
        source: metadata.source || 'web',
        weight: 0.9,
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating favorite playlist activity:', error);
      throw error;
    }
  }

  /**
   * 创建成就动态
   */
  static async createAchievementActivity(userId, achievementData, metadata = {}) {
    try {
      const activityData = {
        user: userId,
        type: 'achievement',
        title: `获得了成就：${achievementData.title}`,
        description: achievementData.description || '',
        target: {
          type: 'achievement',
          id: null,
          snapshot: achievementData
        },
        privacy: 'public',
        source: metadata.source || 'system',
        weight: 2.0, // 成就动态权重很高
        tags: ['成就', achievementData.category || '其他'],
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false,
          extra: {
            achievementType: achievementData.type,
            achievementLevel: achievementData.level
          }
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating achievement activity:', error);
      throw error;
    }
  }

  /**
   * 创建播放里程碑动态
   */
  static async createPlayMilestoneActivity(userId, milestoneData, metadata = {}) {
    try {
      const activityData = {
        user: userId,
        type: 'play_milestone',
        title: `达成了播放里程碑：${milestoneData.title}`,
        description: milestoneData.description || '',
        target: {
          type: 'milestone',
          id: null,
          snapshot: milestoneData
        },
        privacy: 'public',
        source: metadata.source || 'system',
        weight: 1.8,
        tags: ['里程碑', '播放'],
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false,
          extra: {
            milestoneType: milestoneData.type,
            milestoneValue: milestoneData.value
          }
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating play milestone activity:', error);
      throw error;
    }
  }

  /**
   * 创建自定义动态
   */
  static async createCustomActivity(userId, customData, metadata = {}) {
    try {
      const activityData = {
        user: userId,
        type: 'custom',
        title: customData.title,
        description: customData.description || '',
        target: customData.target || { type: null, id: null, snapshot: {} },
        privacy: customData.privacy || 'public',
        source: metadata.source || 'web',
        weight: customData.weight || 1.0,
        tags: customData.tags || [],
        images: customData.images || [],
        location: customData.location || {},
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          deviceInfo: metadata.deviceInfo,
          isMobile: metadata.isMobile || false,
          extra: customData.extra || {}
        }
      };

      return await Activity.createActivity(activityData);
    } catch (error) {
      console.error('Error creating custom activity:', error);
      throw error;
    }
  }

  /**
   * 批量创建动态
   */
  static async createBatchActivities(activities) {
    try {
      const results = [];
      for (const activityData of activities) {
        try {
          const activity = await Activity.createActivity(activityData);
          results.push({ success: true, activity });
        } catch (error) {
          results.push({ success: false, error: error.message });
        }
      }
      return results;
    } catch (error) {
      console.error('Error creating batch activities:', error);
      throw error;
    }
  }

  /**
   * 删除用户动态
   */
  static async deleteUserActivity(userId, activityId) {
    try {
      const activity = await Activity.findOne({
        _id: activityId,
        user: userId
      });

      if (!activity) {
        throw new Error('Activity not found or not authorized');
      }

      await activity.softDelete();
      return activity;
    } catch (error) {
      console.error('Error deleting user activity:', error);
      throw error;
    }
  }

  /**
   * 更新动态隐私设置
   */
  static async updateActivityPrivacy(userId, activityId, privacy) {
    try {
      const activity = await Activity.findOne({
        _id: activityId,
        user: userId
      });

      if (!activity) {
        throw new Error('Activity not found or not authorized');
      }

      activity.privacy = privacy;
      await activity.save();
      return activity;
    } catch (error) {
      console.error('Error updating activity privacy:', error);
      throw error;
    }
  }

  /**
   * 获取用户可以生成动态的行为类型
   */
  static getAvailableActivityTypes() {
    return [
      {
        type: 'upload_music',
        name: '上传音乐',
        description: '用户上传新音乐时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'create_playlist',
        name: '创建歌单',
        description: '用户创建新歌单时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'follow_user',
        name: '关注用户',
        description: '用户关注其他用户时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'like_music',
        name: '点赞音乐',
        description: '用户点赞音乐时自动生成',
        autoGenerate: false, // 可配置是否自动生成
        privacy: 'followers'
      },
      {
        type: 'comment_music',
        name: '评论音乐',
        description: '用户评论音乐时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'share_music',
        name: '分享音乐',
        description: '用户分享音乐时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'favorite_playlist',
        name: '收藏歌单',
        description: '用户收藏歌单时自动生成',
        autoGenerate: false,
        privacy: 'followers'
      },
      {
        type: 'achievement',
        name: '获得成就',
        description: '用户获得成就时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'play_milestone',
        name: '播放里程碑',
        description: '用户达成播放里程碑时自动生成',
        autoGenerate: true,
        privacy: 'public'
      },
      {
        type: 'custom',
        name: '自定义动态',
        description: '用户手动发布的自定义动态',
        autoGenerate: false,
        privacy: 'public'
      }
    ];
  }

  /**
   * 检查是否应该生成动态
   */
  static shouldGenerateActivity(type, userId, targetId = null) {
    // 可以在这里添加更复杂的逻辑，比如：
    // - 检查用户设置是否允许生成此类动态
    // - 检查频率限制（避免刷屏）
    // - 检查目标对象的状态

    const availableTypes = this.getAvailableActivityTypes();
    const typeConfig = availableTypes.find(t => t.type === type);

    if (!typeConfig) {
      return false;
    }

    // 简单的频率限制：同一用户同一类型动态在5分钟内只能生成一次
    const recentThreshold = new Date(Date.now() - 5 * 60 * 1000);

    return Activity.findOne({
      user: userId,
      type: type,
      'target.id': targetId,
      createdAt: { $gte: recentThreshold }
    }).then(existingActivity => {
      return !existingActivity; // 如果没有找到最近的动态，则可以生成
    }).catch(() => {
      return true; // 出错时默认允许生成
    });
  }
}

module.exports = ActivityService;
